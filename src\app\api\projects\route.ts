import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@payload-config'

export async function GET(request: NextRequest) {
  try {
    const payload = await getPayload({ config })

    // Parse query parameters
    const searchParams = request.nextUrl.searchParams
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const category = searchParams.get('category')
    const featured = searchParams.get('featured')
    const status = searchParams.get('status')

    // Build where clause
    const where = {} as any
    if (category) where.category = { equals: category }
    if (featured === 'true') where.featured = { equals: true }
    if (status) where.status = { equals: status }

    // Fetch projects directly from PayloadCMS
    const result = await payload.find({
      collection: 'projects',
      where: Object.keys(where).length > 0 ? where : undefined,
      page,
      limit,
      sort: '-createdAt',
    })

    return NextResponse.json({
      projects: result.docs,
      totalProjects: result.totalDocs,
      page: result.page,
      limit: result.limit,
      totalPages: result.totalPages,
      hasNextPage: result.hasNextPage,
      hasPrevPage: result.hasPrevPage,
    })
  } catch (error) {
    console.error('Projects API error:', error)
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 },
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const body = await request.json()

    // Create new project
    const result = await payload.create({
      collection: 'projects',
      data: body,
    })

    return NextResponse.json(
      {
        success: true,
        message: 'Project created successfully',
        project: result,
      },
      { status: 201 },
    )
  } catch (error) {
    console.error('Projects POST error:', error)
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Failed to create project',
      },
      { status: 500 },
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const body = await request.json()
    const { id, ...updateData } = body

    if (!id) {
      return NextResponse.json(
        {
          error: 'Validation error',
          message: 'Project ID is required for updates',
        },
        { status: 400 },
      )
    }

    // Update project
    const result = await payload.update({
      collection: 'projects',
      id,
      data: updateData,
    })

    return NextResponse.json({
      success: true,
      message: 'Project updated successfully',
      project: result,
    })
  } catch (error) {
    console.error('Projects PUT error:', error)
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Failed to update project',
      },
      { status: 500 },
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const searchParams = request.nextUrl.searchParams
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        {
          error: 'Validation error',
          message: 'Project ID is required for deletion',
        },
        { status: 400 },
      )
    }

    // Delete project
    await payload.delete({
      collection: 'projects',
      id,
    })

    return NextResponse.json({
      success: true,
      message: 'Project deleted successfully',
    })
  } catch (error) {
    console.error('Projects DELETE error:', error)
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Failed to delete project',
      },
      { status: 500 },
    )
  }
}
